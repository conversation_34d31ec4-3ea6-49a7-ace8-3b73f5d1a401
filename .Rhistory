x="MSIscore",
facet.by = c("type","CALLER"),
color = "MSItatus",
scales = "free"
)+
stat_cor()
indel = vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(type=="INDELs",
IMPACT %in% c("MODERATE","HIGH"))%>%
mutate(present = T,
cnt = n_distinct(sample),
.by = SYMBOL
)%>%
slice_max(cnt,n=100,with_ties = T)%>%
right_join(msi%>%distinct(sample),by = "sample")%>%
pivot_wider(id_cols = sample,
names_from = "SYMBOL",
values_from = "present",
values_fn = any,
values_fill = F)%>%
column_to_rownames("sample")%>%
as.matrix()%>%ifelse( "Present", "Absent")
msi%>%
count(patient,time)%>%
pivot_wider(names_from = timei,values_from = n,values_fill = 0)%>%
column_to_rownames("patient")%>%
Heatmap(
cluster_rows = F,
cluster_columns = F,
col = circlize::colorRamp2(c(0,3),c("yellow","blue"))
)
msi%>%
count(patient,time)%>%
pivot_wider(names_from = time,values_from = n,values_fill = 0)%>%
column_to_rownames("patient")%>%
Heatmap(
cluster_rows = F,
cluster_columns = F,
col = circlize::colorRamp2(c(0,3),c("yellow","blue"))
)
msi %>%
count(patient, time) %>%
pivot_wider(names_from = time, values_from = n, values_fill = 0) %>%
column_to_rownames("patient") %>%
as.matrix() %>%
Heatmap(
cluster_rows = FALSE,
cluster_columns = FALSE,
col = circlize::colorRamp2(c(0, 3), c("yellow", "blue")),
cell_fun = function(j, i, x, y, width, height, fill) {
grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
}
)
msi %>%
count(patient, time) %>%
pivot_wider(names_from = time, values_from = n, values_fill = 0) %>%
column_to_rownames("patient") %>%
as.matrix() %>%
Heatmap(
cluster_rows = FALSE,
cluster_columns = FALSE,
col = circlize::colorRamp2(c(0, 3), c("yellow", "blue")),
cell_fun = function(j, i, x, y, width, height, fill) {
grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
}
)
msi %>%
count(patient, time) %>%
pivot_wider(names_from = time, values_from = n, values_fill = 0) %>%
column_to_rownames("patient") %>%
as.matrix() %>%
Heatmap(
cluster_rows = FALSE,
cluster_columns = FALSE,
col = circlize::colorRamp2(c(0, 3), c("yellow", "blue")),
cell_fun = function(j, i, x, y, width, height, fill) {
grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
}
)
p <- msi %>%
count(patient, time) %>%
pivot_wider(names_from = time, values_from = n, values_fill = 0) %>%
column_to_rownames("patient") %>%
as.matrix()
Heatmap(
p,
cluster_rows = FALSE,
cluster_columns = FALSE,
col = circlize::colorRamp2(c(0, 3), c("yellow", "blue")),
cell_fun = function(j, i, x, y, width, height, fill) {
grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
}
)
Heatmap(
p,
cluster_rows = FALSE,
cluster_columns = FALSE,
col = circlize::colorRamp2(c(0, 3), c("grey", "blue")),
cell_fun = function(j, i, x, y, width, height, fill) {
grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
}
)
Heatmap(
p,
cluster_rows = FALSE,
cluster_columns = FALSE,
col = circlize::colorRamp2(c(0, 3), c("grey", "red")),
cell_fun = function(j, i, x, y, width, height, fill) {
grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
}
)
Heatmap(
p,
cluster_rows = FALSE,
cluster_columns = FALSE,
col = circlize::colorRamp2(c(0, 3), c("grey", "red")),
show_heatmap_legend = F,
cell_fun = function(j, i, x, y, width, height, fill) {
grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
}
)
Heatmap(
p,
cluster_rows = FALSE,
cluster_columns = FALSE,
col = circlize::colorRamp2(c(0, 3), c("grey", "red")),
show_heatmap_legend = F,
row_names_side = "left",
cell_fun = function(j, i, x, y, width, height, fill) {
grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
}
)
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
mutate(time = case_when(
time == "Baseline" ~ "Baseline",
T ~ "After"))%>%
ggstripchart(
x="time",y="n",
facet.by = c("type","patient"),
scales = "free",
color = "MSItatus"
)+rotate_x_text()
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
mutate(time = case_when(
time == "Baseline" ~ "Baseline",
T ~ "After"))%>%
ggstripchart(
x="time",y="n",
facet.by = c("type","patient"),
scales = "free",
color = "MSItatus",
shape = "sample_type"
)+rotate_x_text()
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
mutate(time = case_when(
time == "Baseline" ~ "Baseline",
T ~ "After"))%>%
ggstripchart(
x="time",y="n",
facet.by = c("type","patient"),
scales = "free",
color = "MSItatus"
)+rotate_x_text()
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
MSI_status = MSItatus,
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
mutate(time = case_when(
time == "Baseline" ~ "Baseline",
T ~ "After"))%>%
ggstripchart(
x="time",y="n",
facet.by = c("type","patient"),
scales = "free",
color = "MSI_status"
)+rotate_x_text()
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
MSI_status = MSItatus,
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSI_status,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
mutate(time = case_when(
time == "Baseline" ~ "Baseline",
T ~ "After"))%>%
ggstripchart(
x="time",y="n",
facet.by = c("type","patient"),
scales = "free",
color = "MSI_status"
)+rotate_x_text()
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
mutate(
MSI_status = MSItatus
time = case_when(
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
mutate(
MSI_status = MSItatus,
time = case_when(
time == "Baseline" ~ "Baseline",
T ~ "After"))%>%
ggstripchart(
x="time",y="n",
facet.by = c("type","patient"),
scales = "free",
color = "MSI_status"
)+rotate_x_text()
ggsave("mutation_burden.png",width = 8,height = 6)
ggstripchart(
x="time",y="n",
facet.by = c("type","patient"),
scales = "free",
color = "MSI_status"
)+rotate_x_text()
vcf%>%
mutate(
ALT_n = map_int(ALT,str_length),
REF_n = map_int(REF,str_length),
type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
count(sample,type)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
mutate(
MSI_status = MSItatus)%>%
ggstripchart(
x="time",y="n",
facet.by = c("type","patient"),
scales = "free",
color = "MSI_status"
)+rotate_x_text()
ggsave("mutation_burden.png",width = 8,height = 6)
dir("b2/pvactools/102473_Baseline_TA_1/")
dir("b2/pvactools/102473_Baseline_TA_1/combined")
list("b2/pvactools/102473_Baseline_TA_1/combined")
list.files("b2/pvactools/102473_Baseline_TA_1/combined")
list.files("b2/pvactools/106589_W68_TA_1/combined/")
dir("b2/pvactools/")
dir("b2/pvactools/",full.names = T)
dir("b2/pvactools",full.names = T)%>%
map(
~ basename(.x)
)
list.files("b2/pvactools/",recursive = T, full.names = T)
list.files("b2/pvactools/",recursive = T, full.names = T,pattern = "all")
list.files("b2/pvactools/",recursive = T, full.names = T,pattern = "all_epitopes.tsv")
list.files("b2/pvactools/",
recursive = T,
full.names = T,
pattern = "all_epitopes.tsv")
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "all_epitopes.tsv")%>%
map_dfr(
~ read_tsv(.x)%>%
mutate(sample = basename(.x)%>%str_split_1("_")%>%`[`(1))
)
list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "")
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "filtered.tsv")%>%
map_dfr(
~ read_tsv(.x)%>%
mutate(sample = basename(.x)%>%str_split_1("_")%>%`[`(1))
)
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "filtered.tsv")%>%
map_dfr(
~ read_tsv(.x, col_types = col(Start="c"))%>%
mutate(sample = basename(.x)%>%str_split_1("_")%>%`[`(1))
)
?read_tsv
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "filtered.tsv")%>%
map_dfr(
~ read_tsv(.x, col_types = list(Start="c"))%>%
mutate(sample = basename(.x)%>%str_split_1("_")%>%`[`(1))
)
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "filtered.tsv")%>%
map_dfr(
~ read_tsv(.x, col_types = list(Start="c",Stop = "c"))%>%
mutate(sample = basename(.x)%>%str_split_1("_")%>%`[`(1))
)
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "filtered.tsv")%>%
map_dfr(
~ read_tsv(.x, col_types = list(Start="c",Stop = "c",`Transcript Support Level`="c"))%>%
mutate(sample = basename(.x)%>%str_split_1("_")%>%`[`(1))
)
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "filtered.tsv")%>%
map_dfr(
~ read_tsv(.x, col_types = list(.default = "c"))%>%
mutate(sample = basename(.x)%>%str_split_1("_")%>%`[`(1))
)
NeoAg
NeoAg%>%
count(`Variant Type`,Sample)
NeoAg%>%
count(`Variant Type`,sample)
NeoAg%>%
count(`Variant Type`,sample)
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "filtered.tsv")%>%
map_dfr(
~ read_tsv(.x, col_types = list(.default = "c"))%>%
mutate(sample = basename(.x)%>%str_split_1("_")%>%`[`(1))
)
NeoAg%>%
count(`Variant Type`,sample)
NeoAg%>%
count(`Variant Type`,sample)%>%
left_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")
NeoAg
NeoAg%>%
count(`Variant Type`,sample)
mutate(sample = basename(.x)%>%str_remove(".filtered.tsv")
NeoAg = list.files("b2/pvactools",
recursive = T,
full.names = T,
pattern = "filtered.tsv")%>%
map_dfr(
~ read_tsv(.x, col_types = list(.default = "c"))%>%
mutate(sample = basename(.x)%>%str_remove(".filtered.tsv"))
)
NeoAg%>%
count(`Variant Type`,sample)
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%view()
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")
complete(`Variant Type`,sample)
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")%>%
complete(`Variant Type`,sample,fill= list(n=0))
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")%>%
complete(`Variant Type`,sample,fill= list(n=0))%>%
left_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")%>%
complete(`Variant Type`,sample,fill= list(n=0))%>%
left_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
mutate(
MSI_status = MSItatus)%>%
ggstripchart(
x="time",y="n",
facet.by = c("Variant Type","patient"),
scales = "free",
color = "MSI_status"
)+rotate_x_text()
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")%>%
complete(`Variant Type`,sample,fill= list(n=0))%>%
left_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")%>%
complete(`Variant Type`,sample,fill= list(n=0))
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")%>%
complete(`Variant Type`,sample,fill= list(n=0))%>%view()
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")%>%
complete(`Variant Type`,sample,fill= list(n=0))%>%drop_na()
NeoAg%>%
count(`Variant Type`,sample)%>%
full_join(msi%>%distinct(sample),by = "sample")%>%
complete(`Variant Type`,sample,fill= list(n=0))%>%drop_na()%>%
left_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
mutate(
MSI_status = MSItatus)%>%
ggstripchart(
x="time",y="n",
facet.by = c("Variant Type","patient"),
scales = "free",
color = "MSI_status"
)+rotate_x_text()
ggsave("neoAg_burden.png",width = 8,height = 6)
nous_209%>%
ggstripchart(x = "time",y = "FS_pct",shape = "sample_type",facet.by = "patient", ncol = 5,scales ="free")
nous_209 = list.files("msisensor_nous_209//",recursive  = T,full.names = T)%>%
str_subset("_(dis|germline|somatic)$",negate = T)%>%
map_dfr(
~read_tsv(.x)%>%
mutate(sample = basename(.x))
)%>%
separate(sample,into = c("patient","time","sample_type","index"),sep = "_",remove = F)%>%
mutate(FS_pct = `%`,)%>%
left_join(
msi%>%select(sample,MSItatus)
by = "sample"
nous_209 = list.files("msisensor_nous_209//",recursive  = T,full.names = T)%>%
str_subset("_(dis|germline|somatic)$",negate = T)%>%
map_dfr(
~read_tsv(.x)%>%
mutate(sample = basename(.x))
)%>%
separate(sample,into = c("patient","time","sample_type","index"),sep = "_",remove = F)%>%
mutate(FS_pct = `%`,)%>%
left_join(
msi%>%select(sample,MSItatus),
by = "sample"
)
nous_209
nous_209%>%
rename(MSI_status = MSItatus)%>%
ggstripchart(x = "time",y = "FS_pct",color = "MSI_status",shape = "sample_type",facet.by = "patient", ncol = 5,scales ="free")
nous_209%>%
dplyr::rename(MSI_status = MSItatus)%>%
ggstripchart(x = "time",y = "FS_pct",color = "MSI_status",shape = "sample_type",facet.by = "patient", ncol = 5,scales ="free")
nous_209%>%
dplyr::rename(MSI_status = MSItatus)%>%
ggstripchart(x = "time",y = "FS_pct",color = "MSI_status",shape = "sample_type",facet.by = "patient", ncol = 5,scales ="free_x")
