{"id": "BA1454ED", "path": "~/Projects/nouscom/nous_209_gh37_gh38.R", "project_path": "nous_209_gh37_gh38.R", "type": "r_source", "hash": "3591226276", "contents": "", "dirty": false, "created": 1756350205555.0, "source_on_save": false, "relative_order": 4, "properties": {"tempName": "Untitled1", "source_window_id": "", "Source": "Source", "cursorPosition": "113,2", "scrollLine": "94", "marks": "<:113,2\n>:115,-1"}, "folds": "", "lastKnownWriteTime": 1756364122, "encoding": "UTF-8", "collab_server": "", "source_window": "", "last_content_update": 1756364122837, "read_only": false, "read_only_alternatives": []}