{"id": "5E80A441", "path": "~/Projects/nouscom/nous209.R", "project_path": "nous209.R", "type": "r_source", "hash": "3225138836", "contents": "", "dirty": false, "created": 1756387256473.0, "source_on_save": false, "relative_order": 4, "properties": {"tempName": "Untitled1", "source_window_id": "", "Source": "Source", "cursorPosition": "10,26", "scrollLine": "0"}, "folds": "", "lastKnownWriteTime": 1756412417, "encoding": "UTF-8", "collab_server": "", "source_window": "", "last_content_update": 1756412417867, "read_only": false, "read_only_alternatives": []}