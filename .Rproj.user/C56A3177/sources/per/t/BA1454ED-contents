library(readxl)

library(rtracklayer)
library(GenomicRanges)
library(BSgenome.Hsapiens.UCSC.hg38)  # Use hg38 or hg19 depending on your data


gr37 = read_excel("00085472can201072-sup-240540_2_supp_6405217_qd1hgg.xlsx",skip=1)

genome <- BSgenome.Hsapiens.UCSC.hg38


chrs = gr37$`Chromosomic coordinates (GRCh37)`%>%
  map_chr(
    ~str_split_1(.x,":")[1]
  )
  
coords = gr37$`Chromosomic coordinates (GRCh37)`%>%
  map_int(
    ~str_split_1(.x,":")[2]%>%as.numeric()
  )

gr37_range = GRanges(seqnames=chrs,
                     ranges=IRanges(start=coords-5, width=100))

chain <- import.chain("hg19ToHg38.over.chain")

gr38_range <- liftOver(gr37_range, chain)

gr38_seq <- getSeq(genome, gr38_range)
nt_to_bin = function(nt){
  str_c(c(A="00", C="01", G="10", T="11")[str_split_1(nt, "")], collapse = "")%>%
    strtoi(base = 2)
}


extract_seq = function(seq100){
  left_flank_bases = subseq(seq100, start = 1, width = 5)%>%as.character()
  left_flank_binary = left_flank_bases%>%nt_to_bin()
  repeat_unit_bases = subseq(seq100, start = 6, width = 1)%>%as.character()
  repeat_unit_binary = repeat_unit_bases%>%nt_to_bin()

  
  for (i in 7:100){
    if(repeat_unit_bases != as.character(subseq(seq100, start = i, width = 1))){
      break
    }
  }
  repeat_times = i -6
  right_flank_bases = subseq(seq100, start = i, width = 5)%>%as.character()
  right_flank_binary = right_flank_bases%>%nt_to_bin()

  return(list(
    left_flank_bases = left_flank_bases,
    left_flank_binary = left_flank_binary,
    repeat_unit_bases = repeat_unit_bases,
    repeat_unit_binary = repeat_unit_binary,
    repeat_times = repeat_times,
    right_flank_bases = right_flank_bases,
    right_flank_binary = right_flank_binary)%>%as_tibble())
}

target_list = gr38_range%>%as.tibble()%>%
  select(seqnames,start)%>%
  mutate(location = start + 5,
         repeat_unit_length = 1,
         chromosome = seqnames)%>%
  bind_cols(
    gr38_seq%>%
      map_dfr(extract_seq)
  )%>%
  select(chromosome,
         location,
         repeat_unit_length,
         repeat_unit_binary,
         repeat_times,
         left_flank_binary,
         right_flank_binary,
         repeat_unit_bases,
         left_flank_bases,
         right_flank_bases)



target_list%>%write_tsv("nous_209_gh38_target_list.tsv")


gr38_range_tb  = as.tibb(gr38_range,
    \(rg){
      as.tibble(rg)%>%
      mutate(seqs <- getSeq(genome, rg))
      
    }
  )


getSeq(genome, gr38_range)


read_tsv("MSIsensor/102473_Baseline_TA_1/102473_Baseline_TA_1_germline",col_names = NA)%>%nrow

read_tsv("MSIsensor/102473_Baseline_TA_1/102473_Baseline_TA_1_somatic",col_names = NA)%>%nrow

result = read_tsv("MSIsensor/102473_Baseline_TA_1/102473_Baseline_TA_1_dis",col_names = NA)%>%
  mutate(name =  rep(c("loc","N","T"),n()/3),
         ind = rep(1:(n()/3),each = 3))%>%
  pivot_wider(names_from = name,values_from = X1)%>%
  mutate(len_N = map_int(
    N, ~str_remove(.x,"N: ")%>%str_split_1(" ")%>%as.numeric()%>%sum()
  ),
    len_T = map_int(
      T, ~str_remove(.x,"T: ")%>%str_split_1(" ")%>%as.numeric()%>%sum()
    )
  
  )

read_tsv("MSIsensor/102473_Baseline_TA_1/102473_Baseline_TA_1")

