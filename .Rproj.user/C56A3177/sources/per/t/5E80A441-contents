library(tidyverse)
library(ggpubr)

nous_209 = list.files("msisensor_nous_209//",recursive  = T,full.names = T)%>%
  str_subset("_(dis|germline|somatic)$",negate = T)%>%
  map_dfr(
    ~read_tsv(.x)%>%
      mutate(sample = basename(.x))
  )%>%
  separate(sample,into = c("patient","time","sample_type","index"),sep = "_",remove = F)%>%
  mutate(FS_pct = `%`,)%>%
  left_join(
    msi%>%select(sample,MSItatus),
    by = "sample"
  )


nous_209%>%
  dplyr::rename(MSI_status = MSItatus)%>%
  ggstripchart(x = "time",y = "FS_pct",color = "MSI_status",shape = "sample_type",facet.by = "patient", ncol = 5,scales ="free_x")
  
  
