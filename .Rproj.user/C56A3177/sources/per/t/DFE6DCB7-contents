library(tidyverse)
library(ggpubr)
library(ComplexHeatmap)

msi = list.files("MSIsensor/",recursive  = T,full.names = T)%>%
  str_subset("_(dis|germline|somatic)$",negate = T)%>%
  map_dfr(
    ~read_tsv(.x)%>%
      mutate(sample = basename(.x))
  )%>%
  separate(sample,into = c("patient","time","sample_type","index"),sep = "_",remove = F)%>%
  mutate(MSIscore = `%`,
         MSItatus = case_when(
           MSIscore>=10~"MSI-H",
           MSIscore<10~"MSS"
         ))


msi%>%
  ggdotchart(x = "time",y = "MSIscore",shape = "sample_type",facet.by = "patient", ncol = 5,scales="free_x")+
  geom_hline(yintercept = 10,col ="red")
ggsave("msi_score.png",width = 5,height = 6)

msi%>%
  count(patient,time)%>%
  pivot_wider(names_from = time,values_from = n,values_fill = 0)%>%
  column_to_rownames("patient")%>%
  Heatmap(
    cluster_rows = F,
    cluster_columns = F,
    col = circlize::colorRamp2(c(0,3),c("yellow","blue"))
  )
  

p <- msi %>%
  count(patient, time) %>%
  pivot_wider(names_from = time, values_from = n, values_fill = 0) %>%
  column_to_rownames("patient") %>%
  as.matrix()

Heatmap(
  p,
  cluster_rows = FALSE,
  cluster_columns = FALSE,
  col = circlize::colorRamp2(c(0, 3), c("grey", "red")),
  show_heatmap_legend = F,
  row_names_side = "left",
  cell_fun = function(j, i, x, y, width, height, fill) {
    
    grid.text(sprintf("%.0f", p[i, j]), x, y, gp = gpar(fontsize = 10))
  }
)


source("function.R")

vcf = list.files("final_somatic_vcfs/",recursive = T,full.names = T)%>%
  str_subset("vep.vcf$")%>%
  map_dfr(~parse_vcf_csq(.x)%>%
            mutate(sample = basename(.x)%>%str_split_1("\\.")%>%`[`(1))
  )

vcf%>%
  mutate(
    ALT_n = map_int(ALT,str_length),
    REF_n = map_int(REF,str_length),
    type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
  count(sample,type,CALLER)%>%
  left_join(msi%>%distinct(sample,MSItatus,MSIscore),by = "sample")%>%
  ggdotchart(
    x="type",y="n",facet.by = c("type","sample"),color = "CALLER",shape = "MSItatus",scales = "free_y"
  )


vcf%>%
  mutate(
    ALT_n = map_int(ALT,str_length),
    REF_n = map_int(REF,str_length),
    type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
  count(sample,type,CALLER)%>%
  left_join(msi%>%distinct(sample,MSItatus,MSIscore),by = "sample")%>%
  ggscatter(
    y="n",
    x="MSIscore",
    facet.by = c("type","CALLER"),
    color = "MSItatus",
    scales = "free"
  )+
  stat_cor()



indel = vcf%>%
  mutate(
    ALT_n = map_int(ALT,str_length),
    REF_n = map_int(REF,str_length),
    type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
  dplyr::filter(type=="INDELs",
                IMPACT %in% c("MODERATE","HIGH"))%>%
  mutate(present = T,
         cnt = n_distinct(sample),
         .by = SYMBOL
         )%>%
  slice_max(cnt,n=100,with_ties = T)%>%
  right_join(msi%>%distinct(sample),by = "sample")%>%
  pivot_wider(id_cols = sample,
              names_from = "SYMBOL",
              values_from = "present",
              values_fn = any, 
              values_fill = F)%>%
  column_to_rownames("sample")%>%
  as.matrix()%>%ifelse( "Present", "Absent")

# Define colors for each category
  col_fun <- c("Present" = "red", "Absent" = "gray")
  msi_colors <- c(
    "MSS" = "#1f78b4",    # blue
    "MSI-H" = "#e31a1c"    # red
  )
  
  time_colors <- c(
    "Baseline" = "#fb9a99",
    "W52" = "#fdbf6f",
    "W68" = "#fdbf66"
  )
# Make heatmap
Heatmap(
  indel,
  name = "INDELs",
  col = col_fun,
  split = rownames(indel)%>%str_extract("[0-9]{6}"),
  left_annotation = rowAnnotation(
    MSIstatus =msi%>%column_to_rownames("sample")%>%`[`(rownames(indel),"MSItatus"),
    time = msi%>%column_to_rownames("sample")%>%`[`(rownames(indel),"time"),
        col = list(
      MSIstatus = msi_colors,
      time = time_colors
    )
  ),
  row_title_rot = 0
)





SNV = vcf%>%
  mutate(
    ALT_n = map_int(ALT,str_length),
    REF_n = map_int(REF,str_length),
    type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
  dplyr::filter(type!="INDELs",
                IMPACT %in% c("MODERATE","HIGH"))%>%
  mutate(present = T,
         cnt = n_distinct(sample),
         .by = SYMBOL
  )%>%
  slice_max(cnt,n=300,with_ties = T)%>%
  right_join(msi%>%distinct(sample),by = "sample")%>%
  pivot_wider(id_cols = sample,
              names_from = "SYMBOL",
              values_from = "present",
              values_fn = any, 
              values_fill = F)%>%
  column_to_rownames("sample")%>%
  as.matrix()%>%ifelse( "Present", "Absent")

# Define colors for each category
col_fun <- c("Present" = "red", "Absent" = "gray")

# Make heatmap
Heatmap(
  SNV,
  name = "SNV",
  col = col_fun,
  split = rownames(SNV)%>%str_extract("[0-9]{6}"),
  left_annotation = rowAnnotation(
    MSIstatus =msi%>%column_to_rownames("sample")%>%`[`(rownames(SNV),"MSItatus"),
    time = msi%>%column_to_rownames("sample")%>%`[`(rownames(SNV),"time"),
    col = list(
      MSIstatus = msi_colors,
      time = time_colors)
  ),
  row_title_rot = 0
)


vcf%>%
  mutate(
    ALT_n = map_int(ALT,str_length),
    REF_n = map_int(REF,str_length),
    type = case_when(ALT_n!=REF_n~"INDELs",T~"SNVs"))%>%
  dplyr::filter(IMPACT %in% c("MODERATE","HIGH"))%>%
  count(sample,type)%>%
  full_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
  complete(nesting(time,patient,sample,MSItatus),type,fill = list(n=0))%>%
  mutate(
    MSI_status = MSItatus)%>%
  ggstripchart(
    x="time",y="n",
    facet.by = c("type","patient"),
    scales = "free",
    color = "MSI_status"
  )+rotate_x_text()
ggsave("mutation_burden.png",width = 8,height = 6)




dir("b2/pvactools",full.names = T)%>%
  map(
    ~ 
      read_tsv(str_glue("{.x}/combined/all_epitopes"))%>%
      reanm
      basename(.x)
  )

NeoAg = list.files("b2/pvactools",
           recursive = T, 
           full.names = T,
           pattern = "filtered.tsv")%>%
  map_dfr(
    ~ read_tsv(.x, col_types = list(.default = "c"))%>%
      mutate(sample = basename(.x)%>%str_remove(".filtered.tsv"))
  )

NeoAg%>%
  count(`Variant Type`,sample)%>%
  full_join(msi%>%distinct(sample),by = "sample")%>%
  complete(`Variant Type`,sample,fill= list(n=0))%>%drop_na()%>%
  left_join(msi%>%distinct(sample,MSItatus,patient,time),by = "sample")%>%
  mutate(
    MSI_status = MSItatus)%>%
  ggstripchart(
    x="time",y="n",
    facet.by = c("Variant Type","patient"),
    scales = "free",
    color = "MSI_status"
  )+rotate_x_text()
ggsave("neoAg_burden.png",width = 8,height = 6)
